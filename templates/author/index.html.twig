{% extends 'base.html.twig' %}

{% block title %}Liste des auteurs{% endblock %}

{% block body %}
<div class="px-4 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold leading-6 text-gray-900">Liste des auteurs</h1>
            <p class="mt-2 text-sm text-gray-700">Gérez vos auteurs et consultez leurs livres.</p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <a href="{{ path('authors_create') }}" class="block rounded-md bg-blue-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                Ajouter un auteur
            </a>
        </div>
    </div>

    {% if authors is empty %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            <h3 class="mt-2 text-sm font-semibold text-gray-900">Aucun auteur</h3>
            <p class="mt-1 text-sm text-gray-500">Commencez par ajouter votre premier auteur.</p>
            <div class="mt-6">
                <a href="{{ path('authors_create') }}" class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                    </svg>
                    Nouveau auteur
                </a>
            </div>
        </div>
    {% else %}
        <div class="mt-8 flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <div class="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                        {% for author in authors %}
                            <div class="bg-white overflow-hidden shadow rounded-lg border border-gray-200">
                                <div class="px-4 py-5 sm:p-6">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-medium text-gray-900">{{ author.name }}</h3>
                                        <div class="flex space-x-2">
                                            <a href="{{ path('authors_edit', {id: author.id}) }}" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                                Modifier
                                            </a>
                                            <form method="post" action="{{ path('authors_delete', {id: author.id}) }}" class="inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cet auteur ?')">
                                                <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                    Supprimer
                                                </button>
                                            </form>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">Livres :</h4>
                                        {% if author.books is empty %}
                                            <p class="text-sm text-gray-500 italic">Aucun livre</p>
                                        {% else %}
                                            <ul class="space-y-1">
                                                {% for book in author.books %}
                                                    <li class="text-sm text-gray-600 flex items-center">
                                                        <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                                        </svg>
                                                        {{ book.title }}
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
