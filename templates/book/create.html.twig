{% extends 'base.html.twig' %}

{% block title %}<PERSON><PERSON><PERSON> un livre{% endblock %}

{% block body %}
<div class="px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Créer un livre
                </h1>
            </div>
        </div>

        <div class="mt-8">
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
                <form method="post" class="px-4 py-6 sm:p-8">
                    <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                        <div class="sm:col-span-4">
                            <label for="title" class="block text-sm font-medium leading-6 text-gray-900">
                                Titre du livre
                            </label>
                            <div class="mt-2">
                                <input
                                    type="text"
                                    id="title"
                                    name="title"
                                    required
                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-green-600 sm:text-sm sm:leading-6"
                                    placeholder="Entrez le titre du livre"
                                >
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="author" class="block text-sm font-medium leading-6 text-gray-900">
                                Auteur
                            </label>
                            <div class="mt-2">
                                <select
                                    id="author"
                                    name="author_id"
                                    required
                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-green-600 sm:max-w-xs sm:text-sm sm:leading-6"
                                >
                                    <option value="">Choisir un auteur</option>
                                    {% for author in authors %}
                                        <option value="{{ author.id }}">{{ author.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            {% if authors is empty %}
                                <p class="mt-2 text-sm text-red-600">
                                    Aucun auteur disponible.
                                    <a href="{{ path('authors_create') }}" class="font-medium underline">Créer un auteur d'abord</a>
                                </p>
                            {% endif %}
                        </div>

                        <div class="sm:col-span-3">
                            <label for="publishedAt" class="block text-sm font-medium leading-6 text-gray-900">
                                Date de publication
                                <span class="text-gray-500 font-normal">(optionnel)</span>
                            </label>
                            <div class="mt-2">
                                <input
                                    type="date"
                                    id="publishedAt"
                                    name="publishedAt"
                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-green-600 sm:text-sm sm:leading-6"
                                >
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 flex items-center justify-end gap-x-6">
                        <a href="{{ path('books_list') }}" class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700">
                            Annuler
                        </a>
                        <button
                            type="submit"
                            class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600"
                            {% if authors is empty %}disabled{% endif %}
                        >
                            Créer le livre
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
