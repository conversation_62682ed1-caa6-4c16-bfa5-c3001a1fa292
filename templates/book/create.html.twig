{% extends 'base.html.twig' %}

{% block title %}<PERSON><PERSON>er un livre{% endblock %}

{% block body %}
<h1><PERSON><PERSON>er un livre</h1>
<form method="post">
    <div>
        <label for="title">Titre du livre</label>
        <input type="text" id="title" name="title" required>
    </div>
    <div>
        <label for="author">Auteur</label>
        <select id="author" name="author_id" required>
            <option value="">Choisir un auteur</option>
            {% for author in authors %}
                <option value="{{ author.id }}">{{ author.name }}</option>
            {% endfor %}
        </select>
    </div>
    <div>
        <label for="publishedAt">Date de publication (optionnel)</label>
        <input type="date" id="publishedAt" name="publishedAt">
    </div>
    <button type="submit">C<PERSON>er</button>
    <a href="{{ path('books_list') }}">Annuler</a>
</form>
{% endblock %}
