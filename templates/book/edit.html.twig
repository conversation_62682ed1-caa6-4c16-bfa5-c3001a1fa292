{% extends 'base.html.twig' %}

{% block title %}Éditer le livre{% endblock %}

{% block body %}
<h1>Éditer le livre</h1>
<form method="post">
    <div>
        <label for="title">Titre du livre</label>
        <input type="text" id="title" name="title" value="{{ book.title }}" required>
    </div>
    <div>
        <label for="author">Auteur</label>
        <select id="author" name="author_id" required>
            {% for author in authors %}
                <option value="{{ author.id }}" {% if author.id == book.author.id %}selected{% endif %}>
                    {{ author.name }}
                </option>
            {% endfor %}
        </select>
    </div>
    <div>
        <label for="publishedAt">Date de publication (optionnel)</label>
        <input type="date" id="publishedAt" name="publishedAt"
               {% if book.publishedAt %}value="{{ book.publishedAt|date('Y-m-d') }}"{% endif %}>
    </div>
    <button type="submit">Enregistrer</button>
    <a href="{{ path('books_list') }}">Annuler</a>
</form>
{% endblock %}
