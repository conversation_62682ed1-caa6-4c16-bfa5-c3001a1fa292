#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Developer/www/livres/node_modules/.pnpm/webpack-cli@5.1.4_webpack@5.99.9/node_modules/webpack-cli/bin/node_modules:/Users/<USER>/Developer/www/livres/node_modules/.pnpm/webpack-cli@5.1.4_webpack@5.99.9/node_modules/webpack-cli/node_modules:/Users/<USER>/Developer/www/livres/node_modules/.pnpm/webpack-cli@5.1.4_webpack@5.99.9/node_modules:/Users/<USER>/Developer/www/livres/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Developer/www/livres/node_modules/.pnpm/webpack-cli@5.1.4_webpack@5.99.9/node_modules/webpack-cli/bin/node_modules:/Users/<USER>/Developer/www/livres/node_modules/.pnpm/webpack-cli@5.1.4_webpack@5.99.9/node_modules/webpack-cli/node_modules:/Users/<USER>/Developer/www/livres/node_modules/.pnpm/webpack-cli@5.1.4_webpack@5.99.9/node_modules:/Users/<USER>/Developer/www/livres/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../webpack-cli@5.1.4_webpack@5.99.9/node_modules/webpack-cli/bin/cli.js" "$@"
else
  exec node  "$basedir/../../../../../webpack-cli@5.1.4_webpack@5.99.9/node_modules/webpack-cli/bin/cli.js" "$@"
fi
