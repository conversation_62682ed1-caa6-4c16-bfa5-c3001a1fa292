export = MediaSlash0Slash9;
declare class MediaSlash0Slash9 extends BasePlugin {
    /** @param {import('postcss').Result} result */
    constructor(result: import('postcss').Result);
    /**
     * @param {import('postcss').AtRule} rule
     * @return {void}
     */
    detect(rule: import('postcss').AtRule): void;
}
import BasePlugin = require("../plugin");
//# sourceMappingURL=mediaSlash0Slash9.d.ts.map