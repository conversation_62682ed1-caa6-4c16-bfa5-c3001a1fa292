{"name": "sync-rpc", "version": "1.3.6", "main": "lib/index.js", "description": "Run asynchronous commands synchronously by putting them in a separate process", "keywords": [], "files": ["lib/"], "dependencies": {"get-port": "^3.1.0"}, "devDependencies": {"husky": "*", "jest": "*", "lint-staged": "*", "prettier": "*"}, "scripts": {"precommit": "lint-staged", "prettier": "prettier --write \"lib/**/*.js\"", "prettier:check": "prettier --list-different \"lib/**/*.js\"", "test": "jest --coverage", "watch": "jest --coverage --watch"}, "lint-staged": {"*.js": ["prettier --write", "git add"]}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/sync-rpc.git"}, "author": {"name": "Forbes Lindesay", "url": "http://github.com/ForbesLindesay"}, "license": "MIT"}