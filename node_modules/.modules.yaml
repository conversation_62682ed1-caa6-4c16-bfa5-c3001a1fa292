hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.5':
    '@babel/compat-data': private
  '@babel/core@7.27.4':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.27.4)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.4)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.27.4)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.27.5(@babel/core@7.27.4)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.27.3(@babel/core@7.27.4)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.27.3(@babel/core@7.27.4)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.27.5(@babel/core@7.27.4)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.27.2(@babel/core@7.27.4)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.27.4)':
    '@babel/preset-modules': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.4':
    '@babel/traverse': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@discoveryjs/json-ext@0.5.7':
    '@discoveryjs/json-ext': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@leichtgewicht/ip-codec@2.0.5':
    '@leichtgewicht/ip-codec': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nuxt/friendly-errors-webpack-plugin@2.6.0(webpack@5.99.9)':
    '@nuxt/friendly-errors-webpack-plugin': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/bonjour@3.5.13':
    '@types/bonjour': private
  '@types/connect-history-api-fallback@1.5.4':
    '@types/connect-history-api-fallback': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-serve-static-core@5.0.6':
    '@types/express-serve-static-core': private
  '@types/express@4.17.23':
    '@types/express': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/http-proxy@1.17.16':
    '@types/http-proxy': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/minimatch@5.1.2':
    '@types/minimatch': private
  '@types/node-forge@1.3.11':
    '@types/node-forge': private
  '@types/node@24.0.3':
    '@types/node': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/retry@0.12.0':
    '@types/retry': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-index@1.9.4':
    '@types/serve-index': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/sockjs@0.3.36':
    '@types/sockjs': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@webpack-cli/configtest@2.1.1(webpack-cli@5.1.4)(webpack@5.99.9)':
    '@webpack-cli/configtest': private
  '@webpack-cli/info@2.0.2(webpack-cli@5.1.4)(webpack@5.99.9)':
    '@webpack-cli/info': private
  '@webpack-cli/serve@2.0.5(webpack-cli@5.1.4)(webpack@5.99.9)':
    '@webpack-cli/serve': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  accepts@1.3.8:
    accepts: private
  acorn@8.15.0:
    acorn: private
  adjust-sourcemap-loader@4.0.0:
    adjust-sourcemap-loader: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@8.17.1:
    ajv: private
  ansi-html-community@0.0.8:
    ansi-html-community: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  array-union@1.0.2:
    array-union: private
  array-uniq@1.0.3:
    array-uniq: private
  assets-webpack-plugin@7.0.0(webpack@5.99.9):
    assets-webpack-plugin: private
  babel-loader@9.2.1(@babel/core@7.27.4)(webpack@5.99.9):
    babel-loader: private
  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.27.4):
    babel-plugin-polyfill-regenerator: private
  balanced-match@1.0.2:
    balanced-match: private
  batch@0.6.1:
    batch: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@1.20.3:
    body-parser: private
  bonjour-service@1.3.0:
    bonjour-service: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-api@3.0.0:
    caniuse-api: private
  caniuse-lite@1.0.30001724:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  ci-info@3.9.0:
    ci-info: private
  clean-webpack-plugin@4.0.0(webpack@5.99.9):
    clean-webpack-plugin: private
  clone-deep@4.0.1:
    clone-deep: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  colord@2.9.3:
    colord: private
  colorette@2.0.20:
    colorette: private
  commander@4.1.1:
    commander: private
  common-path-prefix@3.0.0:
    common-path-prefix: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.0:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  connect-history-api-fallback@2.0.0:
    connect-history-api-fallback: private
  consola@3.4.2:
    consola: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  core-js-compat@3.43.0:
    core-js-compat: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@9.0.0:
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-declaration-sorter@7.2.0(postcss@8.5.6):
    css-declaration-sorter: private
  css-loader@6.11.0(webpack@5.99.9):
    css-loader: private
  css-minimizer-webpack-plugin@5.0.1(webpack@5.99.9):
    css-minimizer-webpack-plugin: private
  css-select@4.3.0:
    css-select: private
  css-tree@2.3.1:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  cssnano-preset-default@6.1.2(postcss@8.5.6):
    cssnano-preset-default: private
  cssnano-utils@4.0.2(postcss@8.5.6):
    cssnano-utils: private
  cssnano@6.1.2(postcss@8.5.6):
    cssnano: private
  csso@5.0.5:
    csso: private
  debug@4.4.1:
    debug: private
  default-gateway@6.0.3:
    default-gateway: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  del@4.1.1:
    del: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-node@2.1.0:
    detect-node: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  dns-packet@5.6.1:
    dns-packet: private
  dom-converter@0.2.0:
    dom-converter: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  domutils@2.8.0:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.173:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  encodeurl@2.0.0:
    encodeurl: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@2.2.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  envinfo@7.14.0:
    envinfo: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@5.1.1:
    eslint-scope: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  execa@5.1.1:
    execa: private
  express@4.21.2:
    express: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-uri@3.0.6:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastq@1.19.1:
    fastq: private
  faye-websocket@0.11.4:
    faye-websocket: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-cache-dir@4.0.0:
    find-cache-dir: private
  find-up@4.1.0:
    find-up: private
  flat@5.0.2:
    flat: private
  follow-redirects@1.15.9:
    follow-redirects: private
  foreground-child@3.3.1:
    foreground-child: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  fresh@0.5.2:
    fresh: private
  fs-monkey@1.0.6:
    fs-monkey: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-port@3.2.0:
    get-port: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.4.5:
    glob: private
  globals@11.12.0:
    globals: private
  globby@6.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  handle-thing@2.0.1:
    handle-thing: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  hpack.js@2.1.6:
    hpack.js: private
  html-entities@2.6.0:
    html-entities: private
  htmlparser2@6.1.0:
    htmlparser2: private
  http-deceiver@1.2.7:
    http-deceiver: private
  http-errors@2.0.0:
    http-errors: private
  http-parser-js@0.5.10:
    http-parser-js: private
  http-proxy-middleware@2.0.9(@types/express@4.17.23):
    http-proxy-middleware: private
  http-proxy@1.18.1:
    http-proxy: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  icss-utils@5.1.0(postcss@8.5.6):
    icss-utils: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  interpret@3.1.1:
    interpret: private
  ipaddr.js@2.2.0:
    ipaddr.js: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-docker@2.2.1:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-path-cwd@2.2.0:
    is-path-cwd: private
  is-path-in-cwd@2.1.0:
    is-path-in-cwd: private
  is-path-inside@2.1.0:
    is-path-inside: private
  is-plain-obj@3.0.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-stream@2.0.1:
    is-stream: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  jackspeak@3.4.3:
    jackspeak: private
  jest-util@29.7.0:
    jest-util: private
  jest-worker@29.7.0:
    jest-worker: private
  jiti@1.21.7:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json5@2.2.3:
    json5: private
  kind-of@6.0.3:
    kind-of: private
  launch-editor@2.10.0:
    launch-editor: private
  lilconfig@2.1.0:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@2.0.4:
    loader-utils: private
  locate-path@5.0.0:
    locate-path: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  lru-cache@5.1.1:
    lru-cache: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdn-data@2.0.30:
    mdn-data: private
  media-typer@0.3.0:
    media-typer: private
  memfs@3.5.3:
    memfs: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mini-css-extract-plugin@2.9.2(webpack@5.99.9):
    mini-css-extract-plugin: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimatch@9.0.5:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  ms@2.1.3:
    ms: private
  multicast-dns@7.2.5:
    multicast-dns: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  node-forge@1.3.1:
    node-forge: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  obuf@1.1.2:
    obuf: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@8.4.2:
    open: private
  p-limit@2.3.0:
    p-limit: private
  p-locate@4.1.0:
    p-locate: private
  p-map@2.1.0:
    p-map: private
  p-retry@4.6.2:
    p-retry: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-is-inside@1.0.2:
    path-is-inside: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@4.0.1:
    pify: private
  pinkie-promise@2.0.1:
    pinkie-promise: private
  pinkie@2.0.4:
    pinkie: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@7.0.0:
    pkg-dir: private
  postcss-calc@9.0.1(postcss@8.5.6):
    postcss-calc: private
  postcss-colormin@6.1.0(postcss@8.5.6):
    postcss-colormin: private
  postcss-convert-values@6.1.0(postcss@8.5.6):
    postcss-convert-values: private
  postcss-discard-comments@6.0.2(postcss@8.5.6):
    postcss-discard-comments: private
  postcss-discard-duplicates@6.0.3(postcss@8.5.6):
    postcss-discard-duplicates: private
  postcss-discard-empty@6.0.3(postcss@8.5.6):
    postcss-discard-empty: private
  postcss-discard-overridden@6.0.2(postcss@8.5.6):
    postcss-discard-overridden: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-merge-longhand@6.0.5(postcss@8.5.6):
    postcss-merge-longhand: private
  postcss-merge-rules@6.1.1(postcss@8.5.6):
    postcss-merge-rules: private
  postcss-minify-font-values@6.1.0(postcss@8.5.6):
    postcss-minify-font-values: private
  postcss-minify-gradients@6.0.3(postcss@8.5.6):
    postcss-minify-gradients: private
  postcss-minify-params@6.1.0(postcss@8.5.6):
    postcss-minify-params: private
  postcss-minify-selectors@6.0.4(postcss@8.5.6):
    postcss-minify-selectors: private
  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    postcss-modules-local-by-default: private
  postcss-modules-scope@3.2.1(postcss@8.5.6):
    postcss-modules-scope: private
  postcss-modules-values@4.0.0(postcss@8.5.6):
    postcss-modules-values: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-normalize-charset@6.0.2(postcss@8.5.6):
    postcss-normalize-charset: private
  postcss-normalize-display-values@6.0.2(postcss@8.5.6):
    postcss-normalize-display-values: private
  postcss-normalize-positions@6.0.2(postcss@8.5.6):
    postcss-normalize-positions: private
  postcss-normalize-repeat-style@6.0.2(postcss@8.5.6):
    postcss-normalize-repeat-style: private
  postcss-normalize-string@6.0.2(postcss@8.5.6):
    postcss-normalize-string: private
  postcss-normalize-timing-functions@6.0.2(postcss@8.5.6):
    postcss-normalize-timing-functions: private
  postcss-normalize-unicode@6.1.0(postcss@8.5.6):
    postcss-normalize-unicode: private
  postcss-normalize-url@6.0.2(postcss@8.5.6):
    postcss-normalize-url: private
  postcss-normalize-whitespace@6.0.2(postcss@8.5.6):
    postcss-normalize-whitespace: private
  postcss-ordered-values@6.0.2(postcss@8.5.6):
    postcss-ordered-values: private
  postcss-reduce-initial@6.1.0(postcss@8.5.6):
    postcss-reduce-initial: private
  postcss-reduce-transforms@6.0.2(postcss@8.5.6):
    postcss-reduce-transforms: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-svgo@6.0.3(postcss@8.5.6):
    postcss-svgo: private
  postcss-unique-selectors@6.0.4(postcss@8.5.6):
    postcss-unique-selectors: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  pretty-error@4.0.0:
    pretty-error: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  proxy-addr@2.0.7:
    proxy-addr: private
  qs@6.13.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  read-cache@1.0.0:
    read-cache: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  rechoir@0.8.0:
    rechoir: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regex-parser@2.3.1:
    regex-parser: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  renderkid@3.0.0:
    renderkid: private
  require-from-string@2.0.2:
    require-from-string: private
  requires-port@1.0.0:
    requires-port: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-url-loader@5.0.0:
    resolve-url-loader: private
  resolve@1.22.10:
    resolve: private
  retry@0.13.1:
    retry: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  schema-utils@4.3.2:
    schema-utils: private
  select-hose@2.0.0:
    select-hose: private
  selfsigned@2.4.1:
    selfsigned: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-index@1.9.1:
    serve-index: private
  serve-static@1.16.2:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shallow-clone@3.0.1:
    shallow-clone: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  sockjs@0.3.24:
    sockjs: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  spdy-transport@3.0.0:
    spdy-transport: private
  spdy@4.0.2:
    spdy: private
  stackframe@1.3.4:
    stackframe: private
  statuses@2.0.1:
    statuses: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  style-loader@3.3.4(webpack@5.99.9):
    style-loader: private
  stylehacks@6.1.1(postcss@8.5.6):
    stylehacks: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svgo@3.3.2:
    svgo: private
  sync-rpc@1.3.6:
    sync-rpc: private
  tapable@2.2.2:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  thunky@1.1.0:
    thunky: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  type-is@1.6.18:
    type-is: private
  undici-types@7.8.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utila@0.4.0:
    utila: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@8.3.2:
    uuid: private
  vary@1.1.2:
    vary: private
  watchpack@2.4.4:
    watchpack: private
  wbuf@1.7.3:
    wbuf: private
  webpack-cli@5.1.4(webpack@5.99.9):
    webpack-cli: private
  webpack-dev-middleware@5.3.4(webpack@5.99.9):
    webpack-dev-middleware: private
  webpack-dev-server@4.15.2(webpack-cli@5.1.4)(webpack@5.99.9):
    webpack-dev-server: private
  webpack-merge@5.10.0:
    webpack-merge: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.99.9(webpack-cli@5.1.4):
    webpack: private
  websocket-driver@0.7.4:
    websocket-driver: private
  websocket-extensions@0.1.4:
    websocket-extensions: private
  which@2.0.2:
    which: private
  wildcard@2.0.1:
    wildcard: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.2:
    ws: private
  yallist@3.1.1:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yocto-queue@1.2.1:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Tue, 24 Jun 2025 08:01:02 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
