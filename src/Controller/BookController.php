<?php

namespace App\Controller;

use App\Entity\Book;
use App\Repository\BookRepository;
use App\Repository\AuthorRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class BookController extends AbstractController
{
    #[Route('/books', name: 'books_list')]
    public function list(BookRepository $bookRepository): Response
    {
        $books = $bookRepository->findAll();

        return $this->render('book/index.html.twig', [
            'books' => $books,
        ]);
    }

    #[Route('/books/create', name: 'books_create', methods: ['GET', 'POST'])]
    public function create(Request $request, AuthorRepository $authorRepository, EntityManagerInterface $em): Response
    {
        if ($request->isMethod('POST')) {
            $title = $request->request->get('title');
            $authorId = $request->request->get('author_id');
            $publishedAt = $request->request->get('publishedAt');
            if ($title && $authorId) {
                $book = new Book();
                $book->setTitle($title);
                $book->setAuthor($authorRepository->find($authorId));
                $now = new \DateTimeImmutable();
                $book->setCreatedAt($now);
                $book->setUpdatedAt($now);
                if ($publishedAt) {
                    $book->setPublishedAt(new \DateTimeImmutable($publishedAt));
                }
                $em->persist($book);
                $em->flush();
                return $this->redirectToRoute('books_list');
            }
        }
        $authors = $authorRepository->findAll();
        return $this->render('book/create.html.twig', [
            'authors' => $authors,
        ]);
    }

    #[Route('/books/{id}/edit', name: 'books_edit', methods: ['GET', 'POST'])]
    public function edit(Book $book, Request $request, AuthorRepository $authorRepository, EntityManagerInterface $em): Response
    {
        if ($request->isMethod('POST')) {
            $title = $request->request->get('title');
            $authorId = $request->request->get('author_id');
            $publishedAt = $request->request->get('publishedAt');
            if ($title && $authorId) {
                $book->setTitle($title);
                $book->setAuthor($authorRepository->find($authorId));
                $book->setUpdatedAt(new \DateTimeImmutable());
                if ($publishedAt) {
                    $book->setPublishedAt(new \DateTimeImmutable($publishedAt));
                } else {
                    $book->setPublishedAt(null);
                }
                $em->flush();
                return $this->redirectToRoute('books_list');
            }
        }
        $authors = $authorRepository->findAll();
        return $this->render('book/edit.html.twig', [
            'book' => $book,
            'authors' => $authors,
        ]);
    }

    #[Route('/books/{id}/delete', name: 'books_delete', methods: ['POST'])]
    public function delete(Book $book, EntityManagerInterface $em): Response
    {
        $em->remove($book);
        $em->flush();

        return $this->redirectToRoute('books_list');
    }
}
