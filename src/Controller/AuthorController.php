<?php

namespace App\Controller;

use App\Entity\Author;
use App\Repository\AuthorRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class AuthorController extends AbstractController
{
    #[Route('/authors', name: 'authors_list')]
    public function list(AuthorRepository $authorRepository): Response
    {
        $authors = $authorRepository->findAll();

        return $this->render('author/index.html.twig', [
            'authors' => $authors,
        ]);
    }

    #[Route('/authors/create', name: 'authors_create', methods: ['GET', 'POST'])]
    public function create(Request $request, EntityManagerInterface $em): Response
    {
        if ($request->isMethod('POST')) {
            $name = $request->request->get('name');
            if ($name) {
                $author = new Author();
                $author->setName($name);
                $now = new \DateTimeImmutable();
                $author->setCreatedAt($now);
                $author->setUpdatedAt($now);
                $em->persist($author);
                $em->flush();
                return $this->redirectToRoute('authors_list');
            }
        }
        return $this->render('author/create.html.twig');
    }

    #[Route('/authors/{id}/edit', name: 'authors_edit', methods: ['GET', 'POST'])]
    public function edit(Author $author, Request $request, EntityManagerInterface $em): Response
    {
        if ($request->isMethod('POST')) {
            $name = $request->request->get('name');
            if ($name) {
                $author->setName($name);
                $author->setUpdatedAt(new \DateTimeImmutable());
                $em->flush();
                return $this->redirectToRoute('authors_list');
            }
        }
        return $this->render('author/edit.html.twig', [
            'author' => $author,
        ]);
    }

    #[Route('/authors/{id}/delete', name: 'authors_delete', methods: ['POST'])]
    public function delete(Author $author, EntityManagerInterface $em): Response
    {
        $em->remove($author);
        $em->flush();

        return $this->redirectToRoute('authors_list');
    }
}
